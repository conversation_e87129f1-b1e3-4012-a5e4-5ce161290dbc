<?php

namespace App\Modules\DomainCancellationRequest\Services;

use App\Events\EmailSent;
use App\Modules\AdminNotification\Services\AdminNotificationService;
use App\Modules\Domain\Constants\DomainStatus;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Events\DomainHistoryEvent;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DomainCancellationService
{
    public static function instance(): self
    {
        $DomainCancellationService = new self;

        return $DomainCancellationService;
    }
    public function addCancellationRequest($requestCancellation)
    {

        self::updateDomainStatusDeletion($requestCancellation['domainId']);

        // send notif
        AdminNotificationService::instance()->sendDomainDeletionRequest($requestCancellation['domainId']);

        event(new DomainHistoryEvent([
            'domain_id' => $requestCancellation['domainId'],
            'type' => 'DOMAIN_CANCELLATION_REQUEST',
            'user_id' => $requestCancellation['userID'],
            'status' => 'success',
            'message' => 'Domain cancellation request initiated by '.$requestCancellation['email'].'.',
            'payload' => $requestCancellation,
        ]));

        return DB::table('domain_cancellation_requests')->insert([
            'user_id'     => $requestCancellation['userID'],
            'domain_id'   => $requestCancellation['domainId'],
            'reason'      => $requestCancellation['reason'],
            'requested_at' => Carbon::now(),
        ]);
    }

    private function updateDomainStatusDeletion($domainID)
    {
        DB::table('domains')
            ->where('id', $domainID)
            ->update([
                'status' => DomainStatus::IN_PROCESS,
                'updated_at' => Carbon::now(),
            ]);
    }
}
