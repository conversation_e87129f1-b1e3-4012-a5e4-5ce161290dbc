<?php

namespace App\Modules\MarketPlace\Services;

use App\Events\DomainHistoryEvent;
use App\Models\AfternicCommissions;
use App\Models\AfternicOfferHistory;
use App\Models\AfternicOffers;
use App\Models\MarketPlaceDomains;
use App\Models\RegisteredDomain;
use App\Models\User;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Epp\Constants\EppErrorCodes;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Jobs\AfternicDomainTransfer;
use App\Modules\MarketPlace\Jobs\AfternicUpdateTransferFromPoll;
use App\Modules\Notification\Services\TransferNotificationService;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Modules\Transfer\Services\EppTransferService;
use App\Modules\Transfer\Services\TransferDataQueryService;
use App\Modules\Transfer\Services\TransferDomainService;
use App\Modules\Transfer\Services\TransferRefundService;
use App\Traits\UserContact;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Helper\Domain\DomainParser;
use Exception;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;

class DomainTransferService
{
    use UserContact, UserLoggerTrait;

    public static function instance()
    {
        $domainTransferService = new self;

        return $domainTransferService;
    }

    public function callTransfer($array, $domain, $from): void
    {
        $registeredDomains = ($from == 'market') ? RegisteredDomain::where('id', '=', $domain->registered_domain_id)->get() : MarketOfferService::instance()->getRegistrationData($domain->domain_name);

        foreach ($registeredDomains as $regDomain) {
            $email = User::where('id', $domain->user_id)->pluck('email');
            AfternicDomainTransfer::dispatch($array->domain, Crypt::encryptString($array->authCode), property_exists($regDomain, 'year_length') ? $regDomain->year_length . '' : '1', $email[0], $domain->order_id, $domain->registered_domain_id, $domain->user_id, $domain->id, $from);
        }
    }

    public function initiateDomainTransfer($params)
    {
        $response = EppTransferService::instance()->callEppTransferRequest($params['domain'], $params['authCode'], $params['email']);

        if ($this->isSuccessful($response)) {
            $this->handleSuccess($params);
        } else {
            $this->handleFailedTransferRequest($response, $params);
        }
    }

    public function isSuccessful(array $response): bool
    {
        return $response['status'] === Config::get('transfer.response.ok');
    }

    private function handleSuccess($params)
    {
        if($params['from'] == 'market') {
            DB::table('market_place_domains')
                ->where('order_id', $params['orderID'])
                ->update(['status' => MarketConstants::DOMAIN_REQUESTED]);

            $this->getCommissions($params['id'], $params['orderID']);

            $domainId = DB::table('registered_domains')->where('id', $params['registeredDomainId'])->value('domain_id');
            $this->writeEvent($domainId, $params);
        } else if($params['from'] == 'offer') {
            $offer = AfternicOffers::where('order_id', $params['orderID'])->first();
            $offer->update(['offer_status' => AfternicOfferConstants::PAID_TRANSFER_REQUESTED]);

            AfternicOfferHistory::create([
                'afternic_offer_id' => $offer->id,
                'offer_price' => $offer->offer_price,
                'counter_offer_price' => $offer->counter_offer_price,
                'offer_status' => AfternicOfferConstants::PAID_TRANSFER_REQUESTED
            ]);

            $reg = MarketOfferService::instance()->getRegistrationID($offer->domain_name);

            MarketDomainService::instance()->updateByRegisteredDomainId(
                $reg->id,
                $params['userId'],
                ['status' => MarketConstants::DOMAIN_REQUESTED]
            );

            $market = MarketOfferService::instance()->getMarketID($reg->id);

            $this->getCommissions($market->id, $params['orderID']);
            $this->writeEvent($reg->domain_id, $params);
        }

        $fields = ['status' => TransferRequest::PENDING_APPROVAL, 'error_code' => null, 'error_message' => null, 'deleted_at' => null];

        TransferDomainService::instance()->store($params['registeredDomainId']);
        TransferDomainService::instance()->update($params['domain'], $params['registeredDomainId'], $fields, $params['email']);
        TransferNotificationService::instance()->sendTransferPendingApprovalNotif($params['domain'], $params['userId'], $params['email']);

        // send afternic requested status
        app(AuthLogger::class)->info('AfternicDomainTask: sending Afternic status');
        AfternicMiddleware::instance()::sendRequested($params['orderID']);

        app(AuthLogger::class)->info('AfternicDomainTask: Transfer request started. Domain: '.$params['domain'].', user: '.$params['email']);
    }

    public function updateTransferStatusFromPoll($data)
    {
        app(AuthLogger::class)->info('AfternicUpdateTransferFromPoll: Starting ...');

        if (empty($data)) {
            return app(AuthLogger::class)->info('AfternicUpdateTransferFromPoll: Terminating ...');
        }

        foreach ($data as $item) {
            $pollId = DB::table('polls')->where('server_id', $item['id'])->value('id');
            $status = $item['status'];
            $name = strtolower($item['name']);

            AfternicUpdateTransferFromPoll::dispatch($pollId, $status, $name);
        }
    }

    public function getCommissions($id, $orderID): void
    {
        $commissions = AfternicMiddleware::instance()::getCommission($orderID);
        $details = json_decode($commissions->commissionReason);

        AfternicCommissions::create([
            'marketplace_domain_id' => $id,
            'price' => intval($details->input->price),
            'reseller_type' => $details->input->resellerType,
            'reseller_id' => $details->input->resellerId,
            'type' => $details->input->type,
            'boost' => $details->input->boost,
            'commission' => intval($details->steps[0]->output->house),
        ]);
    }

    private function writeEvent($domainId, $params)
    {
        event(new DomainHistoryEvent([
            'domain_id' => $domainId,
            'type' => 'TRANSFER_PURCHASE_COMPLETED',
            'user_id' => $params['userId'],
            'status' => 'success',
            'message' => 'Domain "'.$params['domain'].'" transferred to Afternic',
            'payload' => $params,
        ]));
    }

    private function handleFailedTransferRequest(array $response, array $params): void
    {
        if (isset($response['eppCode'])) {
            $this->handleEppCode($response, $params);
        } else if(array_key_exists('errors', $response)) {
            $this->handleNoEppCode($response, $params);
        } else throw new Exception(QueueErrorTypes::RETRY);
    }

    private function handleNoEppCode(array $response, array $params)
    {
        app(AuthLogger::class)->info('handleFailedTransferRequest handleNoEppCode: ' . json_encode($response));
        
        if(property_exists((object) $response['errors'], 'authentication')) {
            AfternicMiddleware::instance()::sendInvalidAuth($params['orderID']);
            $this->setDomainToPending($params['orderID']);
        } else {
            AfternicMiddleware::instance()::sendDomainLocked($params['orderID']);
            $this->setDomainToPending($params['orderID']);
        }
    }

    private function handleEppCode(array $response, array $params)
    {
        app(AuthLogger::class)->info('handleFailedTransferRequest handleEppCode: ' . json_encode($response));

        switch ($response['eppCode']) {
            case EppErrorCodes::BILLING_FAILURE:
                $this->handleBillingFailure($params['domain'], $params['email']);
                break;
            case EppErrorCodes::DOES_NOT_EXIST:
            case EppErrorCodes::PENDING_STATE_ERROR:
                $this->handleNonExistentOrPendingError($params, $response['eppCode']);
                break;
            case EppErrorCodes::INVALID_AUTH_CODE:
                // @MARKETODO add to history
                AfternicMiddleware::instance()::sendInvalidAuth($params['orderID']);
                $this->setDomainToPending($params['orderID']);
                break;
            case EppErrorCodes::LOCKED_ERROR:
                // @MARKETODO add to history
                AfternicMiddleware::instance()::sendDomainLocked($params['orderID']);
                $this->setDomainToPending($params['orderID']);
                break;
            default:
                $this->handleTransferConflictError($params, $response);
        }

        // event(new DomainHistoryEvent([
        //     'domain_id' => $params['domain_id'],
        //     'type' => 'TRANSFER_PURCHASE_COMPLETED',
        //     'user_id' => $params['user_id'],
        //     'status' => 'failed',
        // ]));
    }

    private function setDomainToPending($orderID): void
    {
        MarketPlaceDomains::where('order_id', '=', $orderID)
            ->update(['status' => MarketConstants::DOMAIN_PENDING]);
    }

    private function handleBillingFailure(array $params): void
    {
        $registryId = DomainParser::getRegistryId($params['domain']);
        RegistryAccountBalanceService::setBalanceToZero($registryId);
        app(AuthLogger::class)->error($this->fromWho('transfer request has failed due to a BILLING FAILURE. Job has been added to the retry logs.', $params['email']));
        throw new Exception(QueueErrorTypes::RETRY);
    }

    private function handleNonExistentOrPendingError(array $params, string $eppCode): void
    {
        app(AuthLogger::class)->error($this->fromWho('handleNonExistentOrPendingError ', $params['email']));
        $transferRequestId = $this->getTransferRequestId($params['registeredDomainId']);
        $refundData = TransferDataQueryService::instance()->getTransferRefundData($transferRequestId);
        TransferRefundService::instance()->executeRefund($refundData, 'Auto-refund');
        TransferRefundService::instance()->handleSystemCancelledDomainRequest($refundData);
        TransferNotificationService::instance()->sendTransferAutoRefundNotif($params['domain'], $params['userId'], $params['email'], $eppCode);
    }

    private function handleTransferConflictError(array $params, array $response): void
    {
        $fields = ['status' => EppDomainStatus::TRANSFER_CLIENT_CONFLICT, 'error_code' => $response['eppCode'], 'error_message' => $response['message'], 'deleted_at' => null];
        TransferDomainService::instance()->update($params['domain'], $params['registeredDomainId'], $fields, $params['email']);
        TransferNotificationService::instance()->sendTransferConflictNotif($params['domain'], $params['userId'], $params['email']);
    }

    private function getTransferRequestId(string $registeredDomainId): string
    {
        return DB::table('transfer_domains')
            ->where('registered_domain_id', $registeredDomainId)
            ->where('status', TransferRequest::PENDING_REQUEST)->value('id');
    }
}
